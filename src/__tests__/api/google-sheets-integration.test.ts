/**
 * Google Sheets 整合測試
 * 測試資料讀取 API（活動資訊、FAQ 等）、資料寫入驗證、容量控制邏輯
 */

import { server } from '../mocks/server';
import { setupTestEnvironment, createTestApiRequest, parseResponse } from '../utils/test-helpers';
import { sheetsTestData } from '../fixtures/form-data';

// 導入 API 路由處理器
import { GET as supportHandler } from '@/app/api/support/route';
import { GET as sessionAvailabilityHandler } from '@/app/api/session-availability/route';

// 導入 Google Sheets 工具函數
import { getSheetData, appendToSheet, getWatchSheetData, getBlogSheetData } from '@/lib/google-sheets';

describe('Google Sheets 整合測試', () => {
  let restoreEnv: () => void;

  beforeAll(() => {
    server.listen();
    restoreEnv = setupTestEnvironment();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
    restoreEnv();
  });

  describe('Google Sheets 基本操作', () => {
    test('應該成功讀取工作表資料', async () => {
      const data = await getSheetData('工作表1!A:AC');
      
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      // 檢查標題行
      const headers = data[0];
      expect(headers).toContain('姓名');
      expect(headers).toContain('電子郵件');
      expect(headers).toContain('場次時間');
    });

    test('應該成功寫入資料到工作表', async () => {
      const testRow = [
        '測試用戶',
        '<EMAIL>',
        '0912345678',
        '台北 07/20（日）13:20',
        'individual',
        new Date().toISOString(),
        'pangea_test_123',
        '3000',
        '2' // AC 欄位 - 保留中
      ];

      const result = await appendToSheet('工作表1!A:AC', [testRow]);
      
      expect(result).toHaveProperty('spreadsheetId');
      expect(result).toHaveProperty('updates');
      expect(result.updates.updatedRows).toBe(1);
    });

    test('應該正確處理空的工作表', async () => {
      // 模擬空工作表的情況
      const data = await getSheetData('空工作表!A:Z');
      
      expect(Array.isArray(data)).toBe(true);
      // 空工作表應該返回空陣列或只有標題行
    });

    test('應該正確處理無效的範圍', async () => {
      try {
        await getSheetData('無效範圍');
        fail('應該拋出錯誤');
      } catch (error) {
        expect(error.message).toContain('無法讀取 Google Sheet');
      }
    });
  });

  describe('/api/support - FAQ 資料讀取', () => {
    test('應該成功獲取所有 FAQ', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/support', {
        method: 'GET'
      });

      const response = await supportHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('faqs');
      expect(data).toHaveProperty('tags');
      expect(data).toHaveProperty('total');
      expect(Array.isArray(data.faqs)).toBe(true);
      expect(Array.isArray(data.tags)).toBe(true);
    });

    test('應該支援標籤篩選', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/support?tag=活動介紹', {
        method: 'GET'
      });

      const response = await supportHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('faqs');
      expect(Array.isArray(data.faqs)).toBe(true);
      
      // 檢查篩選結果
      if (data.faqs.length > 0) {
        const faq = data.faqs[0];
        expect(faq).toHaveProperty('tags');
        expect(faq.tags.some((tag: string) => tag.includes('活動介紹'))).toBe(true);
      }
    });

    test('應該支援關鍵字搜尋', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/support?search=錶匠體驗', {
        method: 'GET'
      });

      const response = await supportHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('faqs');
      expect(Array.isArray(data.faqs)).toBe(true);
      
      // 檢查搜尋結果
      if (data.faqs.length > 0) {
        const faq = data.faqs[0];
        expect(faq).toHaveProperty('question');
        expect(faq).toHaveProperty('answer');
      }
    });

    test('應該正確處理無搜尋結果', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/support?search=不存在的關鍵字', {
        method: 'GET'
      });

      const response = await supportHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('faqs');
      expect(data.faqs).toHaveLength(0);
      expect(data.total).toBe(0);
    });
  });

  describe('/api/session-availability - 場次容量控制', () => {
    test('應該成功獲取場次可用性資訊', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/session-availability', {
        method: 'GET'
      });

      const response = await sessionAvailabilityHandler();
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('data');
      expect(Array.isArray(data.data)).toBe(true);

      // 檢查場次資料結構
      if (data.data.length > 0) {
        const session = data.data[0];
        expect(session).toHaveProperty('sessionTime');
        expect(session).toHaveProperty('maxCapacity');
        expect(session).toHaveProperty('registeredCount');
        expect(session).toHaveProperty('availableSpots');
        expect(session).toHaveProperty('isAvailable');
        expect(session).toHaveProperty('showAvailability');
      }
    });

    test('應該正確計算場次名額', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/session-availability', {
        method: 'GET'
      });

      const response = await sessionAvailabilityHandler();
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      
      // 驗證名額計算邏輯
      data.data.forEach((session: any) => {
        expect(session.availableSpots).toBe(session.maxCapacity - session.registeredCount);
        expect(session.isAvailable).toBe(session.availableSpots > 0);
        
        // 剩餘名額少於3時才顯示
        if (session.availableSpots < 3 && session.availableSpots > 0) {
          expect(session.showAvailability).toBe(true);
        } else {
          expect(session.showAvailability).toBe(false);
        }
      });
    });

    test('應該正確處理場次已滿的情況', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/session-availability', {
        method: 'GET'
      });

      const response = await sessionAvailabilityHandler();
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      
      // 檢查是否有已滿的場次
      const fullSessions = data.data.filter((session: any) => !session.isAvailable);
      fullSessions.forEach((session: any) => {
        expect(session.availableSpots).toBe(0);
        expect(session.isAvailable).toBe(false);
      });
    });

    test('應該支援快取機制', async () => {
      // 第一次請求
      const request1 = createTestApiRequest('http://localhost:3000/api/session-availability', {
        method: 'GET'
      });

      const response1 = await sessionAvailabilityHandler();
      const { status: status1, data: data1 } = await parseResponse(response1);

      expect(status1).toBe(200);
      expect(data1).toHaveProperty('cached', false);

      // 第二次請求（應該使用快取）
      const request2 = createTestApiRequest('http://localhost:3000/api/session-availability', {
        method: 'GET'
      });

      const response2 = await sessionAvailabilityHandler();
      const { status: status2, data: data2 } = await parseResponse(response2);

      expect(status2).toBe(200);
      // 根據快取實作調整期望結果
      if (data2.cached !== undefined) {
        expect(data2).toHaveProperty('cached');
        expect(data2).toHaveProperty('cacheAge');
      }
    });
  });

  describe('容量控制邏輯測試', () => {
    test('應該正確統計報名狀態', async () => {
      // 模擬報名資料
      const mockRegistrationData = [
        ['姓名', '場次時間', 'AC欄位'], // 標題行
        ['用戶1', '台北 07/20（日）13:20', '1'], // 已確認
        ['用戶2', '台北 07/20（日）13:20', '2'], // 保留中
        ['用戶3', '台北 07/20（日）13:20', '3'], // 已取消
        ['用戶4', '台北 07/20（日）15:20', '1'], // 已確認
      ];

      // 這裡需要模擬 Google Sheets 返回上述資料
      const data = await getSheetData('工作表1!A:AC');
      
      // 驗證資料結構
      expect(Array.isArray(data)).toBe(true);
      if (data.length > 1) {
        const dataRow = data[1];
        expect(dataRow.length).toBeGreaterThan(2);
      }
    });

    test('應該正確處理場次時間格式', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/session-availability', {
        method: 'GET'
      });

      const response = await sessionAvailabilityHandler();
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      
      // 檢查場次時間格式
      data.data.forEach((session: any) => {
        expect(typeof session.sessionTime).toBe('string');
        expect(session.sessionTime.length).toBeGreaterThan(0);
        
        // 檢查是否包含預期的場次格式
        const validSessionFormats = [
          '台北',
          '台中',
          '有意願但無合適時間地點'
        ];
        
        const hasValidFormat = validSessionFormats.some(format => 
          session.sessionTime.includes(format)
        );
        expect(hasValidFormat).toBe(true);
      });
    });

    test('應該正確處理多場次報名', async () => {
      // 測試用戶報名多個場次的情況
      const multiSessionData = [
        ['姓名', '場次時間', 'AC欄位'],
        ['用戶1', '台北 07/20（日）13:20,台北 07/20（日）15:20', '1'],
      ];

      // 驗證多場次報名只計算第一個場次
      const sessionTimes = '台北 07/20（日）13:20,台北 07/20（日）15:20';
      const firstSession = sessionTimes.split(',')[0];
      expect(firstSession).toBe('台北 07/20（日）13:20');
    });
  });

  describe('錯誤處理', () => {
    test('應該正確處理 Google Sheets API 錯誤', async () => {
      try {
        // 嘗試讀取不存在的工作表
        await getSheetData('error-sheet!A:Z');
        fail('應該拋出錯誤');
      } catch (error) {
        expect(error.message).toContain('無法讀取 Google Sheet');
      }
    });

    test('應該正確處理網路連線錯誤', async () => {
      // 這個測試需要模擬網路錯誤
      try {
        await getSheetData('network-error!A:Z');
        fail('應該拋出錯誤');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('應該正確處理權限錯誤', async () => {
      // 模擬權限不足的情況
      try {
        await getSheetData('permission-denied!A:Z');
        fail('應該拋出錯誤');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('應該正確處理資料格式錯誤', async () => {
      try {
        // 嘗試寫入無效格式的資料
        await appendToSheet('工作表1!A:Z', [['invalid', null, undefined]]);
      } catch (error) {
        // 根據實際實作調整期望結果
        expect(error).toBeDefined();
      }
    });
  });

  describe('效能優化測試', () => {
    test('應該使用指定欄位範圍查詢', async () => {
      // 測試只查詢必要欄位以提升效能
      const specificRangeData = await getSheetData('工作表1!B:D,AC:AC');
      
      expect(Array.isArray(specificRangeData)).toBe(true);
      // 驗證返回的資料結構符合指定範圍
    });

    test('應該正確處理大量資料', async () => {
      // 測試處理大量報名資料的效能
      const largeDataSet = await getSheetData('工作表1!A:AC');
      
      expect(Array.isArray(largeDataSet)).toBe(true);
      
      // 測試處理時間（應該在合理範圍內）
      const startTime = Date.now();
      const processedData = largeDataSet.slice(1); // 移除標題行
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(1000); // 應該在1秒內完成
    });

    test('應該支援批次操作', async () => {
      // 測試批次寫入多筆資料
      const batchData = [
        ['用戶1', '<EMAIL>', '0912345678'],
        ['用戶2', '<EMAIL>', '0987654321'],
        ['用戶3', '<EMAIL>', '0911111111']
      ];

      const result = await appendToSheet('工作表1!A:C', batchData);
      
      expect(result).toHaveProperty('updates');
      expect(result.updates.updatedRows).toBe(batchData.length);
    });
  });
});
