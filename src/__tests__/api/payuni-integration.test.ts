/**
 * PayUni 付款整合 API 測試
 * 測試 /api/payment/create, /api/payment/callback, /api/payment/query 等付款相關端點
 */

import { server } from '../mocks/server';
import { setupTestEnvironment, createTestApiRequest, parseResponse, generateTestEmail, generateTestOrderNo } from '../utils/test-helpers';
import { paymentTestData, orderStatusTestData } from '../fixtures/form-data';

// 導入 API 路由處理器
import { POST as createPaymentHandler } from '@/app/api/create-payment/route';
import { POST as paymentCallbackHandler, GET as paymentCallbackGetHandler } from '@/app/api/payment/callback/route';
import { GET as orderQueryHandler } from '@/app/api/order/[orderNo]/route';
import { POST as orderStatusHandler } from '@/app/api/order-status/route';
import { POST as retryPaymentHandler } from '@/app/api/retry-payment/route';
import { POST as webhookHandler, GET as webhookGetHandler } from '@/app/api/webhook/payment/route';

describe('PayUni 付款整合 API 測試', () => {
  let restoreEnv: () => void;

  beforeAll(() => {
    server.listen();
    restoreEnv = setupTestEnvironment();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
    restoreEnv();
  });

  describe('/api/create-payment', () => {
    test('應該成功建立信用卡付款訂單', async () => {
      const paymentData = {
        orderNo: generateTestOrderNo(),
        eventName: '錶匠體驗機芯拆解',
        eventPrice: 3000,
        userName: '測試用戶',
        userEmail: generateTestEmail()
      };

      const request = createTestApiRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: paymentData
      });

      const response = await createPaymentHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('paymentUrl');
      expect(data).toHaveProperty('orderNo', paymentData.orderNo);
      expect(data.paymentUrl).toContain('payuni.com.tw');
    });

    test('應該正確設定 ATM 轉帳到期日期', async () => {
      const paymentData = {
        orderNo: generateTestOrderNo(),
        eventName: '錶匠體驗機芯拆解',
        eventPrice: 3000,
        userName: '測試用戶',
        userEmail: generateTestEmail()
      };

      const request = createTestApiRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: paymentData
      });

      const response = await createPaymentHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('success', true);
      
      // 驗證 ATM 到期日期邏輯
      const now = new Date();
      const taiwanTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
      const taiwanHour = taiwanTime.getUTCHours();
      
      // 根據台灣時間判斷到期日期
      const expectedDays = taiwanHour >= 14 ? 3 : 2;
      const expectedExpireDate = new Date();
      expectedExpireDate.setDate(expectedExpireDate.getDate() + expectedDays);
      
      // 這裡可以進一步驗證回應中的到期日期設定
    });

    test('應該拒絕無效的付款金額', async () => {
      const invalidPaymentData = {
        orderNo: generateTestOrderNo(),
        eventName: '錶匠體驗機芯拆解',
        eventPrice: 0, // 無效金額
        userName: '測試用戶',
        userEmail: generateTestEmail()
      };

      const request = createTestApiRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: invalidPaymentData
      });

      const response = await createPaymentHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(400);
      expect(data).toHaveProperty('error');
    });

    test('應該正確處理重複的訂單編號', async () => {
      const duplicateOrderNo = generateTestOrderNo();
      const paymentData = {
        orderNo: duplicateOrderNo,
        eventName: '錶匠體驗機芯拆解',
        eventPrice: 3000,
        userName: '測試用戶',
        userEmail: generateTestEmail()
      };

      // 第一次建立付款
      const request1 = createTestApiRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: paymentData
      });

      const response1 = await createPaymentHandler(request1);
      const { status: status1 } = await parseResponse(response1);
      expect(status1).toBe(200);

      // 第二次使用相同訂單編號
      const request2 = createTestApiRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: paymentData
      });

      const response2 = await createPaymentHandler(request2);
      const { status: status2, data: data2 } = await parseResponse(response2);

      // 根據業務邏輯，可能允許重複建立或拒絕
      if (status2 === 400) {
        expect(data2).toHaveProperty('error');
        expect(data2.error).toContain('訂單編號已存在');
      } else {
        expect(status2).toBe(200);
      }
    });
  });

  describe('/api/payment/callback', () => {
    test('應該正確處理 PayUni POST 回調', async () => {
      const callbackData = {
        Status: 'SUCCESS',
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'payuni_test_456',
        TradeAmt: '3000',
        PaymentType: '1',
        TradeStatus: '1',
        PaymentDay: '2025-01-01 12:00:00'
      };

      const request = createTestApiRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: callbackData
      });

      const response = await paymentCallbackHandler(request);
      const { status } = await parseResponse(response);

      // 回調處理應該重定向到結果頁面
      expect(status).toBe(302);
    });

    test('應該正確處理 PayUni GET 回調', async () => {
      const queryParams = new URLSearchParams({
        Status: 'SUCCESS',
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'payuni_test_456',
        TradeAmt: '3000',
        PaymentType: '1',
        TradeStatus: '1'
      });

      const request = createTestApiRequest(
        `http://localhost:3000/api/payment/callback?${queryParams.toString()}`,
        { method: 'GET' }
      );

      const response = await paymentCallbackGetHandler(request);
      const { status } = await parseResponse(response);

      expect(status).toBe(302);
    });

    test('應該正確處理付款失敗的回調', async () => {
      const failedCallbackData = {
        Status: 'FAIL',
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'payuni_test_456',
        TradeAmt: '3000',
        PaymentType: '1',
        TradeStatus: '2', // 付款失敗
        Message: '付款失敗'
      };

      const request = createTestApiRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: failedCallbackData
      });

      const response = await paymentCallbackHandler(request);
      const { status } = await parseResponse(response);

      expect(status).toBe(302);
    });
  });

  describe('/api/order/[orderNo]', () => {
    test('應該成功查詢存在的訂單', async () => {
      const orderNo = 'pangea_test_123';
      const email = '<EMAIL>';

      const request = createTestApiRequest(
        `http://localhost:3000/api/order/${orderNo}?email=${email}`,
        { method: 'GET' }
      );

      const response = await orderQueryHandler(request, { 
        params: Promise.resolve({ orderNo }) 
      });
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('orderInfo');
      expect(data.orderInfo).toHaveProperty('orderNo', orderNo);
    });

    test('應該拒絕查詢不存在的訂單', async () => {
      const nonExistentOrderNo = 'pangea_nonexistent_123';

      const request = createTestApiRequest(
        `http://localhost:3000/api/order/${nonExistentOrderNo}`,
        { method: 'GET' }
      );

      const response = await orderQueryHandler(request, { 
        params: Promise.resolve({ orderNo: nonExistentOrderNo }) 
      });
      const { status, data } = await parseResponse(response);

      expect(status).toBe(404);
      expect(data).toHaveProperty('error');
      expect(data.error).toContain('找不到指定的訂單');
    });

    test('應該拒絕缺少訂單編號的請求', async () => {
      const request = createTestApiRequest(
        'http://localhost:3000/api/order/',
        { method: 'GET' }
      );

      const response = await orderQueryHandler(request, { 
        params: Promise.resolve({ orderNo: '' }) 
      });
      const { status, data } = await parseResponse(response);

      expect(status).toBe(400);
      expect(data).toHaveProperty('error', '缺少訂單號碼');
    });
  });

  describe('/api/order-status', () => {
    test('應該成功查詢訂單狀態', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: { orderNo: 'pangea_test_123' }
      });

      const response = await orderStatusHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('orderInfo');
    });

    test('應該拒絕空的訂單編號', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: { orderNo: '' }
      });

      const response = await orderStatusHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(400);
      expect(data).toHaveProperty('error', '請輸入訂單號碼');
    });
  });

  describe('/api/retry-payment', () => {
    test('應該成功建立重新付款訂單', async () => {
      const retryData = {
        originalOrderNo: 'pangea_test_123',
        email: generateTestEmail()
      };

      const request = createTestApiRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: retryData
      });

      const response = await retryPaymentHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('newOrderNo');
      expect(data).toHaveProperty('paymentUrl');
      expect(data.newOrderNo).not.toBe(retryData.originalOrderNo);
    });

    test('應該拒絕重新付款不存在的訂單', async () => {
      const retryData = {
        originalOrderNo: 'pangea_nonexistent_123',
        email: generateTestEmail()
      };

      const request = createTestApiRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: retryData
      });

      const response = await retryPaymentHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(404);
      expect(data).toHaveProperty('error');
      expect(data.error).toContain('找不到原始訂單');
    });
  });

  describe('/api/webhook/payment', () => {
    test('應該正確處理 PayUni webhook 通知', async () => {
      const webhookData = {
        Status: 'SUCCESS',
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'payuni_test_456',
        TradeAmt: '3000',
        PaymentType: '1',
        TradeStatus: '1',
        PaymentDay: '2025-01-01 12:00:00'
      };

      const request = createTestApiRequest('http://localhost:3000/api/webhook/payment', {
        method: 'POST',
        body: webhookData
      });

      const response = await webhookHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('message', 'Webhook received');
    });

    test('應該回應 webhook 健康檢查', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/webhook/payment', {
        method: 'GET'
      });

      const response = await webhookGetHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('status', 'ok');
      expect(data).toHaveProperty('message', 'PayUni Webhook endpoint is reachable');
    });

    test('應該正確處理退款通知', async () => {
      const refundWebhookData = {
        Status: 'SUCCESS',
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'payuni_test_456',
        TradeAmt: '3000',
        PaymentType: '1',
        TradeStatus: '1',
        RefundStatus: '2', // 已退款
        RefundAmt: '3000',
        PaymentDay: '2025-01-01 12:00:00'
      };

      const request = createTestApiRequest('http://localhost:3000/api/webhook/payment', {
        method: 'POST',
        body: refundWebhookData
      });

      const response = await webhookHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('success', true);
    });
  });

  describe('PayUni 錯誤處理', () => {
    test('應該正確處理 PayUni API 錯誤', async () => {
      // 模擬 PayUni API 錯誤回應
      const paymentData = {
        orderNo: 'error_order_123',
        eventName: '錶匠體驗機芯拆解',
        eventPrice: 3000,
        userName: '測試用戶',
        userEmail: generateTestEmail()
      };

      const request = createTestApiRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: paymentData
      });

      // 這裡需要配置 MSW 來模擬 PayUni 錯誤回應
      const response = await createPaymentHandler(request);
      const { status, data } = await parseResponse(response);

      // 根據錯誤類型調整期望結果
      if (status !== 200) {
        expect(data).toHaveProperty('error');
      }
    });

    test('應該正確處理網路超時', async () => {
      // 這個測試需要模擬網路超時情況
      const paymentData = {
        orderNo: 'timeout_order_123',
        eventName: '錶匠體驗機芯拆解',
        eventPrice: 3000,
        userName: '測試用戶',
        userEmail: generateTestEmail()
      };

      const request = createTestApiRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: paymentData
      });

      // 設定較短的超時時間來測試
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Timeout')), 1000);
      });

      try {
        await Promise.race([createPaymentHandler(request), timeoutPromise]);
      } catch (error) {
        expect(error.message).toBe('Timeout');
      }
    });
  });
});
