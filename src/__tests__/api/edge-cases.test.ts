/**
 * 邊緣案例和錯誤處理測試
 * 測試驗證失敗、付款重試、容量限制、網路錯誤等邊緣情境
 */

import { server } from '../mocks/server';
import { setupTestEnvironment, createTestApiRequest, parseResponse, generateTestEmail, generateTestOrderNo, delay } from '../utils/test-helpers';
import { eventRegistrationTestData } from '../fixtures/form-data';

// 導入 API 路由處理器
import { POST as eventRegistrationHandler } from '@/app/api/event-registration/route';
import { POST as createPaymentHandler } from '@/app/api/create-payment/route';
import { POST as retryPaymentHandler } from '@/app/api/retry-payment/route';
import { GET as sessionAvailabilityHandler } from '@/app/api/session-availability/route';

describe('邊緣案例和錯誤處理測試', () => {
  let restoreEnv: () => void;

  beforeAll(() => {
    server.listen();
    restoreEnv = setupTestEnvironment();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
    restoreEnv();
  });

  describe('表單驗證邊緣案例', () => {
    test('應該拒絕包含惡意腳本的輸入', async () => {
      const maliciousData = {
        ...eventRegistrationTestData.valid,
        name: '<script>alert("XSS")</script>',
        email: generateTestEmail(),
        questions: '<img src="x" onerror="alert(1)">',
        needsPayment: true,
        eventPrice: 3000
      };

      const request = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: maliciousData
      });

      const response = await eventRegistrationHandler(request);
      const { status, data } = await parseResponse(response);

      // 應該清理或拒絕惡意輸入
      if (status === 200) {
        // 如果接受請求，應該清理惡意腳本
        expect(data.name).not.toContain('<script>');
        expect(data.questions).not.toContain('<img');
      } else {
        expect(status).toBe(400);
        expect(data).toHaveProperty('error');
      }
    });

    test('應該拒絕超長的輸入字串', async () => {
      const longString = 'a'.repeat(10000);
      const longInputData = {
        ...eventRegistrationTestData.valid,
        name: longString,
        email: generateTestEmail(),
        needsPayment: true,
        eventPrice: 3000
      };

      const request = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: longInputData
      });

      const response = await eventRegistrationHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(400);
      expect(data).toHaveProperty('error');
      expect(data.error).toContain('輸入長度超過限制');
    });

    test('應該正確處理特殊字符', async () => {
      const specialCharData = {
        ...eventRegistrationTestData.valid,
        name: '張測試 & 李測試',
        email: generateTestEmail(),
        watchBrands: 'Rolex & Omega, Patek Philippe',
        questions: '我想了解 "機芯拆解" 的詳細過程？',
        needsPayment: true,
        eventPrice: 3000,
        eventName: '錶匠體驗機芯拆解'
      };

      const request = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: specialCharData
      });

      const response = await eventRegistrationHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(200);
      expect(data).toHaveProperty('success', true);
    });

    test('應該拒絕無效的 JSON 格式', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: '{ invalid json format',
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await eventRegistrationHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(400);
      expect(data).toHaveProperty('error');
    });

    test('應該正確處理空的請求體', async () => {
      const request = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: ''
      });

      const response = await eventRegistrationHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(400);
      expect(data).toHaveProperty('error');
    });
  });

  describe('付款重試邊緣案例', () => {
    test('應該正確處理多次重試付款', async () => {
      const originalOrderNo = generateTestOrderNo();
      const email = generateTestEmail();

      // 第一次重試
      const retryData1 = {
        originalOrderNo,
        email
      };

      const request1 = createTestApiRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: retryData1
      });

      const response1 = await retryPaymentHandler(request1);
      const { status: status1, data: data1 } = await parseResponse(response1);

      if (status1 === 200) {
        expect(data1).toHaveProperty('newOrderNo');
        
        // 第二次重試（使用新的訂單號）
        const retryData2 = {
          originalOrderNo: data1.newOrderNo,
          email
        };

        const request2 = createTestApiRequest('http://localhost:3000/api/retry-payment', {
          method: 'POST',
          body: retryData2
        });

        const response2 = await retryPaymentHandler(request2);
        const { status: status2, data: data2 } = await parseResponse(response2);

        if (status2 === 200) {
          expect(data2).toHaveProperty('newOrderNo');
          expect(data2.newOrderNo).not.toBe(data1.newOrderNo);
        }
      }
    });

    test('應該防止過於頻繁的重試請求', async () => {
      const originalOrderNo = generateTestOrderNo();
      const email = generateTestEmail();
      const retryData = { originalOrderNo, email };

      // 快速連續發送多個重試請求
      const requests = Array.from({ length: 5 }, () =>
        createTestApiRequest('http://localhost:3000/api/retry-payment', {
          method: 'POST',
          body: retryData
        })
      );

      const responses = await Promise.all(
        requests.map(request => retryPaymentHandler(request))
      );

      const parsedResponses = await Promise.all(
        responses.map(response => parseResponse(response))
      );

      // 檢查是否有適當的頻率限制
      const successCount = parsedResponses.filter(({ status }) => status === 200).length;
      const errorCount = parsedResponses.filter(({ status }) => status !== 200).length;

      // 根據實際的頻率限制邏輯調整期望結果
      expect(successCount + errorCount).toBe(5);
    });

    test('應該正確處理訂單編號長度限制', async () => {
      // PayUni 有訂單編號長度限制
      const longOrderNo = 'pangea_' + 'a'.repeat(100);
      const paymentData = {
        orderNo: longOrderNo,
        eventName: '錶匠體驗機芯拆解',
        eventPrice: 3000,
        userName: '測試用戶',
        userEmail: generateTestEmail()
      };

      const request = createTestApiRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: paymentData
      });

      const response = await createPaymentHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(400);
      expect(data).toHaveProperty('error');
      expect(data.error).toContain('訂單編號長度超過限制');
    });
  });

  describe('容量限制邊緣案例', () => {
    test('應該正確處理場次容量邊界情況', async () => {
      // 模擬場次即將額滿的情況
      const nearFullSessionData = {
        ...eventRegistrationTestData.valid,
        sessionTimes: ['台北 07/20（日）15:20'], // 假設這個場次只剩1個名額
        email: generateTestEmail(),
        needsPayment: true,
        eventPrice: 3000,
        eventName: '錶匠體驗機芯拆解'
      };

      const request = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: nearFullSessionData
      });

      const response = await eventRegistrationHandler(request);
      const { status, data } = await parseResponse(response);

      // 根據實際的容量控制邏輯調整期望結果
      if (status === 200) {
        expect(data).toHaveProperty('success', true);
      } else if (status === 400) {
        expect(data).toHaveProperty('error');
        expect(data.error).toContain('場次已滿');
      }
    });

    test('應該正確處理同時報名衝突', async () => {
      // 模擬多個用戶同時報名最後一個名額
      const sessionData = {
        ...eventRegistrationTestData.valid,
        sessionTimes: ['台北 07/20（日）15:20'],
        needsPayment: true,
        eventPrice: 3000,
        eventName: '錶匠體驗機芯拆解'
      };

      const requests = Array.from({ length: 3 }, () => ({
        ...sessionData,
        email: generateTestEmail()
      })).map(data =>
        createTestApiRequest('http://localhost:3000/api/event-registration', {
          method: 'POST',
          body: data
        })
      );

      const responses = await Promise.all(
        requests.map(request => eventRegistrationHandler(request))
      );

      const parsedResponses = await Promise.all(
        responses.map(response => parseResponse(response))
      );

      // 檢查是否正確處理並發報名
      const successCount = parsedResponses.filter(({ status }) => status === 200).length;
      const errorCount = parsedResponses.filter(({ status }) => status === 400).length;

      expect(successCount + errorCount).toBe(3);
      // 根據容量控制邏輯，可能只有部分請求成功
    });

    test('應該正確更新場次可用性快取', async () => {
      // 測試場次可用性快取的更新機制
      const request1 = createTestApiRequest('http://localhost:3000/api/session-availability', {
        method: 'GET'
      });

      const response1 = await sessionAvailabilityHandler();
      const { status: status1, data: data1 } = await parseResponse(response1);

      expect(status1).toBe(200);
      const initialAvailability = data1.data;

      // 模擬新的報名
      const registrationData = {
        ...eventRegistrationTestData.valid,
        email: generateTestEmail(),
        needsPayment: true,
        eventPrice: 3000,
        eventName: '錶匠體驗機芯拆解'
      };

      const registrationRequest = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: registrationData
      });

      await eventRegistrationHandler(registrationRequest);

      // 再次查詢場次可用性
      const request2 = createTestApiRequest('http://localhost:3000/api/session-availability', {
        method: 'GET'
      });

      const response2 = await sessionAvailabilityHandler();
      const { status: status2, data: data2 } = await parseResponse(response2);

      expect(status2).toBe(200);
      // 檢查快取是否正確更新
    });
  });

  describe('網路錯誤和超時處理', () => {
    test('應該正確處理 Google Sheets API 超時', async () => {
      // 模擬 Google Sheets API 超時
      const timeoutData = {
        ...eventRegistrationTestData.valid,
        email: generateTestEmail(),
        needsPayment: true,
        eventPrice: 3000,
        eventName: '錶匠體驗機芯拆解'
      };

      const request = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: timeoutData
      });

      // 設定較短的超時時間
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Timeout')), 2000);
      });

      try {
        await Promise.race([eventRegistrationHandler(request), timeoutPromise]);
      } catch (error) {
        expect(error.message).toBe('Timeout');
      }
    });

    test('應該正確處理 PayUni API 網路錯誤', async () => {
      const paymentData = {
        orderNo: 'network_error_test',
        eventName: '錶匠體驗機芯拆解',
        eventPrice: 3000,
        userName: '測試用戶',
        userEmail: generateTestEmail()
      };

      const request = createTestApiRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: paymentData
      });

      const response = await createPaymentHandler(request);
      const { status, data } = await parseResponse(response);

      // 根據錯誤處理邏輯調整期望結果
      if (status !== 200) {
        expect(data).toHaveProperty('error');
        expect(data.error).toContain('網路錯誤');
      }
    });

    test('應該正確處理部分服務不可用', async () => {
      // 模擬部分外部服務不可用的情況
      const registrationData = {
        ...eventRegistrationTestData.valid,
        email: generateTestEmail(),
        needsPayment: true,
        eventPrice: 3000,
        eventName: '錶匠體驗機芯拆解'
      };

      const request = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: registrationData
      });

      const response = await eventRegistrationHandler(request);
      const { status, data } = await parseResponse(response);

      // 即使部分服務不可用，核心功能應該仍能運作
      if (status === 200) {
        expect(data).toHaveProperty('success', true);
      } else {
        expect(data).toHaveProperty('error');
      }
    });
  });

  describe('資料一致性測試', () => {
    test('應該確保訂單編號唯一性', async () => {
      const orderNos = new Set();
      const requests = Array.from({ length: 10 }, () => {
        const data = {
          ...eventRegistrationTestData.valid,
          email: generateTestEmail(),
          needsPayment: true,
          eventPrice: 3000,
          eventName: '錶匠體驗機芯拆解'
        };

        return createTestApiRequest('http://localhost:3000/api/event-registration', {
          method: 'POST',
          body: data
        });
      });

      const responses = await Promise.all(
        requests.map(request => eventRegistrationHandler(request))
      );

      const parsedResponses = await Promise.all(
        responses.map(response => parseResponse(response))
      );

      parsedResponses.forEach(({ status, data }) => {
        if (status === 200 && data.orderNo) {
          expect(orderNos.has(data.orderNo)).toBe(false);
          orderNos.add(data.orderNo);
        }
      });

      // 確保所有成功的請求都有唯一的訂單編號
      expect(orderNos.size).toBeGreaterThan(0);
    });

    test('應該正確處理資料庫事務回滾', async () => {
      // 模擬需要回滾的情況
      const invalidData = {
        ...eventRegistrationTestData.valid,
        email: 'invalid-email-format',
        needsPayment: true,
        eventPrice: 3000
      };

      const request = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: invalidData
      });

      const response = await eventRegistrationHandler(request);
      const { status, data } = await parseResponse(response);

      expect(status).toBe(400);
      expect(data).toHaveProperty('error');
      
      // 確保沒有部分資料被寫入
      // 這裡需要根據實際的事務處理邏輯進行驗證
    });

    test('應該正確處理重複提交檢測', async () => {
      const duplicateData = {
        ...eventRegistrationTestData.valid,
        email: generateTestEmail(),
        needsPayment: true,
        eventPrice: 3000,
        eventName: '錶匠體驗機芯拆解'
      };

      // 快速連續提交相同資料
      const request1 = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: duplicateData
      });

      const request2 = createTestApiRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: duplicateData
      });

      const [response1, response2] = await Promise.all([
        eventRegistrationHandler(request1),
        eventRegistrationHandler(request2)
      ]);

      const [parsed1, parsed2] = await Promise.all([
        parseResponse(response1),
        parseResponse(response2)
      ]);

      // 檢查重複提交的處理結果
      expect(parsed1.status).toBe(200);
      
      // 根據重複提交檢測邏輯調整期望結果
      if (parsed2.status === 400) {
        expect(parsed2.data).toHaveProperty('error');
        expect(parsed2.data.error).toContain('重複提交');
      } else {
        expect(parsed2.status).toBe(200);
      }
    });
  });
});
